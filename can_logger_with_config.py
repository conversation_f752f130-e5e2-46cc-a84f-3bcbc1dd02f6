
import can
import yaml
import time
import threading
import requests
import os
import logging
import signal
import sys
from datetime import datetime

CONFIG_PATH = "can_config.yaml"
ERROR_LOG = "can_error.log"
SERVICE_LOG = "can_service.log"

# 加载配置
with open(CONFIG_PATH, 'r') as f:
    config = yaml.safe_load(f)

CAN_INTERFACE = config.get("can_interface", "can0")
BITRATE = config.get("bitrate", 250000)
CAN_ID_LIST = set([int(i, 16) if isinstance(i, str) else i for i in config.get("can_id_list", [])])
UPLOAD_INTERVAL = config.get("upload_interval", 10)
UPLOAD_URL = config.get("upload_url", "")
LOG_PATH = config.get("local_log_path", "can_data_log_new.json")
ENABLE_LOCAL_LOG = config.get("enable_local_log", True)
VIN = config.get("vin", "UNKNOWN")

# 错误限频控制器
class ErrorRateLimiter:
    def __init__(self):
        self.last_error = ""
        self.count = 0
        self.last_log_time = 0

    def should_log(self, msg):
        now = time.time()
        if msg != self.last_error:
            self.last_error = msg
            self.count = 1
            self.last_log_time = now
            return True
        else:
            wait_time = min(60 * (2 ** self.count), 1800)
            if now - self.last_log_time >= wait_time:
                self.count += 1
                self.last_log_time = now
                return True
            return False

error_limiter = ErrorRateLimiter()

class CanListener(can.Listener):
    def on_message_received(self, msg):
        if msg.arbitration_id in CAN_ID_LIST:
            data_str = ' '.join(f"{b:02x}" for b in msg.data).upper()
            can_data_cache[f"{msg.arbitration_id}"] = data_str

# 初始化日志器
# 配置错误日志
error_logger = logging.getLogger('error')
error_handler = logging.FileHandler(ERROR_LOG)
error_handler.setLevel(logging.ERROR)
error_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
error_handler.setFormatter(error_formatter)
error_logger.addHandler(error_handler)
error_logger.setLevel(logging.ERROR)

# 配置服务日志（启动、停止等）
service_logger = logging.getLogger('service')
service_handler = logging.FileHandler(SERVICE_LOG)
service_handler.setLevel(logging.INFO)
service_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
service_handler.setFormatter(service_formatter)
service_logger.addHandler(service_handler)
service_logger.setLevel(logging.INFO)

# 同时输出到控制台
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(service_formatter)
service_logger.addHandler(console_handler)

# 初始化缓存字典
can_data_cache = {}  # {hex(can_id): "xx xx xx xx ..."}

# 全局变量用于优雅退出
shutdown_flag = threading.Event()

def signal_handler(signum, frame):
    """信号处理函数，用于优雅退出"""
    signal_name = signal.Signals(signum).name
    service_logger.info(f"收到退出信号 {signal_name} ({signum})，开始优雅关闭...")
    print(f"[!] 收到退出信号 {signal_name}，正在关闭服务...")
    shutdown_flag.set()

def setup_can_interface():
    force = config.get("force_init_can", False)
    if not force:
        try:
            result = os.popen(f"ip link show {CAN_INTERFACE}").read()
            if "state UP" in result:
                print(f"[✓] {CAN_INTERFACE} 已启动")
                return
        except Exception as e:
            pass

    print(f"[!] 启动 {CAN_INTERFACE} ...")
    os.system(f"sudo ip link set {CAN_INTERFACE} type can bitrate {BITRATE}")
    os.system(f"sudo ifconfig {CAN_INTERFACE} up")
    print(f"[✓] {CAN_INTERFACE} 初始化完成")



def uploader():
    service_logger.info("数据上传线程已启动")
    while not shutdown_flag.is_set():
        # 使用 wait 替代 sleep，这样可以响应 shutdown_flag
        if shutdown_flag.wait(UPLOAD_INTERVAL):
            break

        try:
            payload = {
                "vin": VIN,
                "time": int(time.time() * 1000)
            }
            payload.update(can_data_cache)
            res = requests.post(UPLOAD_URL, json=payload, timeout=5)
            if res.status_code != 200:
                raise Exception(f"Upload failed: {res.status_code}")
            if ENABLE_LOCAL_LOG:
                with open(LOG_PATH, 'a') as f:
                    f.write(str(payload) + "\n")
        except Exception as e:
            msg = f"[UPLOAD ERROR] {e}"
            if error_limiter.should_log(str(e)):
                print(msg)
                error_logger.error(msg)

    service_logger.info("数据上传线程已停止")

def main():
    try:
        # 记录启动日志
        service_logger.info("=== CAN Logger 服务启动 ===")
        service_logger.info(f"配置文件: {CONFIG_PATH}")
        service_logger.info(f"CAN接口: {CAN_INTERFACE}")
        service_logger.info(f"波特率: {BITRATE}")
        service_logger.info(f"监听CAN ID数量: {len(CAN_ID_LIST)}")
        service_logger.info(f"上传间隔: {UPLOAD_INTERVAL}秒")
        service_logger.info(f"上传URL: {UPLOAD_URL}")
        service_logger.info(f"VIN: {VIN}")
        service_logger.info(f"本地日志: {'启用' if ENABLE_LOCAL_LOG else '禁用'}")
        if ENABLE_LOCAL_LOG:
            service_logger.info(f"本地日志路径: {LOG_PATH}")

        # 注册信号处理器
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

        # 初始化CAN接口
        setup_can_interface()
        service_logger.info("CAN接口初始化完成")

        # 启动CAN总线监听
        bus = can.interface.Bus(channel=CAN_INTERFACE, bustype='socketcan')
        listener = CanListener()
        notifier = can.Notifier(bus, [listener])
        service_logger.info("CAN总线监听已启动")

        # 启动上传线程
        upload_thread = threading.Thread(target=uploader, daemon=False)
        upload_thread.start()

        service_logger.info("CAN Logger 服务启动完成")
        print("[✓] CAN Logger 服务已启动")

        # 主循环，等待退出信号
        try:
            while not shutdown_flag.is_set():
                shutdown_flag.wait(60)  # 每60秒检查一次
        except KeyboardInterrupt:
            service_logger.info("收到键盘中断信号")
            shutdown_flag.set()

        # 优雅关闭
        service_logger.info("正在关闭CAN Logger服务...")
        print("[!] 正在关闭服务...")

        # 等待上传线程结束
        upload_thread.join(timeout=10)
        if upload_thread.is_alive():
            service_logger.warning("上传线程未能在10秒内正常结束")

        # 关闭CAN总线
        notifier.stop()
        bus.shutdown()
        service_logger.info("CAN总线已关闭")

        service_logger.info("=== CAN Logger 服务已停止 ===")
        print("[✓] CAN Logger 服务已停止")

    except Exception as e:
        error_msg = f"服务启动失败: {e}"
        service_logger.error(error_msg)
        error_logger.error(error_msg)
        print(f"[✗] {error_msg}")
        sys.exit(1)

if __name__ == "__main__":
    main()
